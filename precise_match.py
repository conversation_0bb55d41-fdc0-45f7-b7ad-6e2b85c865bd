#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import chardet
from difflib import SequenceMatcher

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取文件: {file_path}")
            return df
        except Exception as e:
            continue
    
    # 如果都失败了，尝试自动检测编码
    try:
        detected_encoding = detect_encoding(file_path)
        df = pd.read_csv(file_path, encoding=detected_encoding)
        print(f"成功使用检测到的编码 {detected_encoding} 读取文件: {file_path}")
        return df
    except Exception as e:
        print(f"自动检测编码也失败了: {e}")
        return None

def extract_keywords(text):
    """提取关键词"""
    keywords = []
    # 道路类型
    road_types = ['路', '街', '道', '巷', '胡同']
    # 设施类型
    facility_types = ['隧道', '立交', '桥', '下穿', '涵洞', '门']
    # 方位词
    directions = ['东', '西', '南', '北', '中', '上', '下']
    
    for word in text:
        if word in road_types or word in facility_types or word in directions:
            keywords.append(word)
    
    # 提取连续的中文字符作为地名
    current_word = ""
    for char in text:
        if '\u4e00' <= char <= '\u9fff':  # 中文字符
            current_word += char
        else:
            if len(current_word) >= 2:
                keywords.append(current_word)
            current_word = ""
    
    if len(current_word) >= 2:
        keywords.append(current_word)
    
    return list(set(keywords))

def calculate_precise_similarity(risk_point, standard_name, standard_location):
    """计算精确相似度"""
    # 完全匹配
    if risk_point == standard_name:
        return 1.0, "完全匹配"
    
    # 使用difflib计算相似度
    name_sim = SequenceMatcher(None, risk_point, standard_name).ratio()
    location_sim = SequenceMatcher(None, risk_point, standard_location).ratio()
    
    # 关键词匹配
    risk_keywords = extract_keywords(risk_point)
    name_keywords = extract_keywords(standard_name)
    location_keywords = extract_keywords(standard_location)
    
    # 计算关键词重叠度
    name_keyword_overlap = len(set(risk_keywords) & set(name_keywords)) / max(len(risk_keywords), 1)
    location_keyword_overlap = len(set(risk_keywords) & set(location_keywords)) / max(len(risk_keywords), 1)
    
    # 综合评分
    final_score = (name_sim * 0.4 + location_sim * 0.3 + 
                   name_keyword_overlap * 0.2 + location_keyword_overlap * 0.1)
    
    match_type = "相似度匹配"
    if final_score >= 0.9:
        match_type = "高度相似"
    elif final_score >= 0.7:
        match_type = "中度相似"
    elif final_score >= 0.5:
        match_type = "低度相似"
    
    return final_score, match_type

def find_precise_matches(df_standard, df_target):
    """精确匹配积水点"""
    matches = []
    unmatched_target = []
    used_standard_indices = set()
    
    # 首先进行完全匹配
    print("进行完全匹配...")
    for target_idx, target_row in df_target.iterrows():
        risk_point = str(target_row['风险点']).strip()
        
        for standard_idx, standard_row in df_standard.iterrows():
            if standard_idx in used_standard_indices:
                continue
                
            standard_name = str(standard_row['点位名称']).strip()
            
            if risk_point == standard_name:
                matches.append({
                    'target_row': target_row,
                    'standard_row': standard_row,
                    'similarity_score': 1.0,
                    'match_type': '完全匹配'
                })
                used_standard_indices.add(standard_idx)
                break
    
    # 对未匹配的项进行相似度匹配
    print("进行相似度匹配...")
    matched_target_indices = {match['target_row'].name for match in matches}
    
    for target_idx, target_row in df_target.iterrows():
        if target_idx in matched_target_indices:
            continue
            
        risk_point = str(target_row['风险点']).strip()
        target_district = str(target_row['行政区']).strip()
        
        best_match = None
        best_score = 0.0
        best_standard_idx = None
        best_match_type = ""
        
        for standard_idx, standard_row in df_standard.iterrows():
            if standard_idx in used_standard_indices:
                continue
                
            standard_name = str(standard_row['点位名称']).strip()
            standard_location = str(standard_row['具体位置']).strip()
            standard_district = str(standard_row['行政区域']).strip()
            
            score, match_type = calculate_precise_similarity(risk_point, standard_name, standard_location)
            
            # 行政区匹配加分
            if target_district == standard_district:
                score += 0.1
            
            if score > best_score and score >= 0.6:  # 提高匹配阈值
                best_score = score
                best_match = standard_row
                best_standard_idx = standard_idx
                best_match_type = match_type
        
        if best_match is not None:
            matches.append({
                'target_row': target_row,
                'standard_row': best_match,
                'similarity_score': best_score,
                'match_type': best_match_type
            })
            used_standard_indices.add(best_standard_idx)
        else:
            unmatched_target.append(target_row)
    
    # 找出标准文件中未匹配的项
    unmatched_standard = []
    for idx, standard_row in df_standard.iterrows():
        if idx not in used_standard_indices:
            unmatched_standard.append(standard_row)
    
    return matches, unmatched_target, unmatched_standard

def create_match_report(matches, unmatched_target, unmatched_standard):
    """创建匹配报告"""
    report = []
    report.append("=" * 80)
    report.append("积水点匹配报告")
    report.append("=" * 80)
    report.append(f"总匹配数: {len(matches)}")
    report.append(f"3h50mm.csv未匹配数: {len(unmatched_target)}")
    report.append(f"2025积水点.csv未匹配数: {len(unmatched_standard)}")
    report.append("")
    
    # 按匹配类型分组
    match_types = {}
    for match in matches:
        match_type = match['match_type']
        if match_type not in match_types:
            match_types[match_type] = []
        match_types[match_type].append(match)
    
    for match_type, type_matches in match_types.items():
        report.append(f"{match_type}: {len(type_matches)} 个")
        for i, match in enumerate(type_matches[:5]):  # 显示前5个
            report.append(f"  {i+1}. {match['target_row']['风险点']} -> {match['standard_row']['点位名称']} (相似度: {match['similarity_score']:.2f})")
        if len(type_matches) > 5:
            report.append(f"  ... 还有 {len(type_matches) - 5} 个")
        report.append("")
    
    return "\n".join(report)

def main():
    # 读取文件
    print("正在读取文件...")
    df_standard = read_csv_with_encoding('2025积水点.csv')
    df_target = read_csv_with_encoding('3h50mm.csv')
    
    if df_standard is None or df_target is None:
        print("文件读取失败")
        return
    
    # 精确匹配
    print("\n开始精确匹配...")
    matches, unmatched_target, unmatched_standard = find_precise_matches(df_standard, df_target)
    
    # 生成报告
    report = create_match_report(matches, unmatched_target, unmatched_standard)
    print(report)
    
    # 保存报告
    with open('match_report.txt', 'w', encoding='utf-8') as f:
        f.write(report)
    print("匹配报告已保存为 match_report.txt")
    
    # 创建增强的CSV文件
    enhanced_df = df_target.copy()
    new_columns = ['编码', '点位名称', '具体位置', '行政区域', '排水分区', '行业主管部门', '所属街办', '经度', '纬度', '程度']
    for col in new_columns:
        enhanced_df[col] = ''
    
    # 填充匹配的数据
    for match in matches:
        target_idx = match['target_row'].name
        standard_row = match['standard_row']
        
        for col in new_columns:
            if col in standard_row:
                enhanced_df.at[target_idx, col] = standard_row[col]
    
    # 保存增强文件
    enhanced_df.to_csv('3h50mm_precise_enhanced.csv', index=False, encoding='utf-8-sig')
    print("精确匹配的增强文件已保存为 3h50mm_precise_enhanced.csv")

if __name__ == "__main__":
    main()
