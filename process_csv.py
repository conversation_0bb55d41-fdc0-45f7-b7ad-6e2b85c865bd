#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import chardet

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding']

def read_csv_with_encoding(file_path):
    """尝试不同编码读取CSV文件"""
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    
    for encoding in encodings:
        try:
            df = pd.read_csv(file_path, encoding=encoding)
            print(f"成功使用 {encoding} 编码读取文件: {file_path}")
            return df
        except Exception as e:
            print(f"使用 {encoding} 编码读取失败: {e}")
            continue
    
    # 如果都失败了，尝试自动检测编码
    try:
        detected_encoding = detect_encoding(file_path)
        print(f"检测到的编码: {detected_encoding}")
        df = pd.read_csv(file_path, encoding=detected_encoding)
        print(f"成功使用检测到的编码 {detected_encoding} 读取文件: {file_path}")
        return df
    except Exception as e:
        print(f"自动检测编码也失败了: {e}")
        return None

def calculate_similarity(str1, str2):
    """计算两个字符串的相似度"""
    str1 = str1.strip()
    str2 = str2.strip()

    # 完全匹配
    if str1 == str2:
        return 1.0

    # 包含关系
    if str1 in str2 or str2 in str1:
        return 0.8

    # 计算共同字符数
    common_chars = 0
    for char in str1:
        if char in str2:
            common_chars += 1

    if len(str1) == 0 or len(str2) == 0:
        return 0.0

    return common_chars / max(len(str1), len(str2))

def find_matches(df_standard, df_target):
    """查找匹配的积水点"""
    matches = []
    unmatched_target = []
    used_standard_indices = set()

    # 为每个目标风险点找到最佳匹配
    for target_idx, target_row in df_target.iterrows():
        risk_point = str(target_row['风险点']).strip()
        target_district = str(target_row['行政区']).strip()

        best_match = None
        best_score = 0.0
        best_standard_idx = None

        # 遍历所有标准积水点
        for standard_idx, standard_row in df_standard.iterrows():
            if standard_idx in used_standard_indices:
                continue

            standard_name = str(standard_row['点位名称']).strip()
            standard_location = str(standard_row['具体位置']).strip()
            standard_district = str(standard_row['行政区域']).strip()

            # 计算相似度
            name_similarity = calculate_similarity(risk_point, standard_name)
            location_similarity = calculate_similarity(risk_point, standard_location)

            # 综合相似度（名称权重更高）
            total_similarity = name_similarity * 0.7 + location_similarity * 0.3

            # 如果行政区匹配，给予额外加分
            if target_district == standard_district:
                total_similarity += 0.1

            # 特殊关键词匹配加分
            keywords = ['隧道', '立交', '下穿', '桥', '门', '路口', '十字']
            for keyword in keywords:
                if keyword in risk_point and keyword in standard_name:
                    total_similarity += 0.1
                    break

            if total_similarity > best_score and total_similarity > 0.4:  # 设置最低匹配阈值
                best_score = total_similarity
                best_match = standard_row
                best_standard_idx = standard_idx

        if best_match is not None:
            matches.append({
                'target_row': target_row,
                'standard_row': best_match,
                'similarity_score': best_score,
                'match_type': f'相似度匹配 ({best_score:.2f})'
            })
            used_standard_indices.add(best_standard_idx)
        else:
            unmatched_target.append(target_row)

    # 找出标准文件中未匹配的项
    unmatched_standard = []
    for idx, standard_row in df_standard.iterrows():
        if idx not in used_standard_indices:
            unmatched_standard.append(standard_row)

    return matches, unmatched_target, unmatched_standard

def create_enhanced_csv(matches, df_target, output_file='3h50mm_enhanced.csv'):
    """创建增强的CSV文件"""
    # 复制原始目标文件
    enhanced_df = df_target.copy()

    # 添加新列
    new_columns = ['编码', '点位名称', '具体位置', '行政区域', '排水分区', '行业主管部门', '所属街办', '经度', '纬度', '程度']
    for col in new_columns:
        enhanced_df[col] = ''

    # 填充匹配的数据
    for match in matches:
        target_idx = match['target_row'].name
        standard_row = match['standard_row']

        for col in new_columns:
            if col in standard_row:
                enhanced_df.at[target_idx, col] = standard_row[col]

    # 保存文件
    enhanced_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"增强后的文件已保存为: {output_file}")

    return enhanced_df

def main():
    # 读取两个文件
    print("正在读取 2025积水点.csv...")
    df_standard = read_csv_with_encoding('2025积水点.csv')

    print("\n正在读取 3h50mm.csv...")
    df_target = read_csv_with_encoding('3h50mm.csv')

    if df_standard is None or df_target is None:
        print("文件读取失败，程序退出")
        return

    # 显示文件结构
    print("\n=== 2025积水点.csv 文件结构 ===")
    print("列名:", df_standard.columns.tolist())
    print("前5行数据:")
    print(df_standard.head())
    print(f"总行数: {len(df_standard)}")

    print("\n=== 3h50mm.csv 文件结构 ===")
    print("列名:", df_target.columns.tolist())
    print("前5行数据:")
    print(df_target.head())
    print(f"总行数: {len(df_target)}")

    # 查找匹配项
    print("\n=== 开始匹配积水点信息 ===")
    matches, unmatched_target, unmatched_standard = find_matches(df_standard, df_target)

    print(f"找到匹配项: {len(matches)} 个")
    print(f"3h50mm.csv中未匹配项: {len(unmatched_target)} 个")
    print(f"2025积水点.csv中未匹配项: {len(unmatched_standard)} 个")

    # 显示匹配结果
    if matches:
        print("\n=== 匹配结果示例 ===")
        for i, match in enumerate(matches[:5]):  # 显示前5个匹配项
            print(f"\n匹配 {i+1}:")
            print(f"  3h50mm.csv风险点: {match['target_row']['风险点']}")
            print(f"  2025积水点.csv点位名称: {match['standard_row']['点位名称']}")
            print(f"  匹配类型: {match['match_type']}")

    # 创建增强的CSV文件
    print("\n=== 创建增强的CSV文件 ===")
    enhanced_df = create_enhanced_csv(matches, df_target)

    # 显示未匹配的项目
    if unmatched_target:
        print(f"\n=== 3h50mm.csv中未匹配的风险点 (前10个) ===")
        for i, row in enumerate(unmatched_target[:10]):
            print(f"{i+1}. {row['风险点']} ({row['行政区']})")

    if unmatched_standard:
        print(f"\n=== 2025积水点.csv中未匹配的点位 (前10个) ===")
        for i, row in enumerate(unmatched_standard[:10]):
            print(f"{i+1}. {row['点位名称']} ({row['行政区域']})")

if __name__ == "__main__":
    main()
